import 'package:flutter/foundation.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';

class DashboardProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  List<FamilyMember> _familyMembers = [];
  List<Medicine> _activeMedicines = [];
  Map<String, int> _stats = {};
  bool _isLoading = false;
  String? _error;

  List<FamilyMember> get familyMembers => _familyMembers;
  List<Medicine> get activeMedicines => _activeMedicines;
  Map<String, int> get stats => _stats;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadDashboardData() async {
    _setLoading(true);
    _setError(null);
    
    try {
      // Load family members
      _familyMembers = await _databaseHelper.getFamilyMembers();
      
      // Load active medicines
      _activeMedicines = await _getActiveMedicines();
      
      // Calculate stats
      _stats = await _calculateStats();
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load dashboard data: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<List<Medicine>> _getActiveMedicines() async {
    List<Medicine> allMedicines = [];
    
    for (final member in _familyMembers) {
      final visits = await _databaseHelper.getDoctorVisits(member.id!);
      for (final visit in visits) {
        final medicines = await _databaseHelper.getMedicines(visit.id!);
        final activeMedicines = medicines.where((medicine) => 
          medicine.endDate.isAfter(DateTime.now())
        ).toList();
        allMedicines.addAll(activeMedicines);
      }
    }
    
    return allMedicines;
  }

  Future<Map<String, int>> _calculateStats() async {
    int totalVisits = 0;
    int totalDiagnoses = 0;
    int totalReports = 0;
    
    for (final member in _familyMembers) {
      final visits = await _databaseHelper.getDoctorVisits(member.id!);
      totalVisits += visits.length;
      
      for (final visit in visits) {
        final diagnoses = await _databaseHelper.getDiagnoses(visit.id!);
        final reports = await _databaseHelper.getMedicalReports(visit.id!);
        totalDiagnoses += diagnoses.length;
        totalReports += reports.length;
      }
    }
    
    return {
      'familyMembers': _familyMembers.length,
      'doctorVisits': totalVisits,
      'diagnoses': totalDiagnoses,
      'medicalReports': totalReports,
      'activeMedicines': _activeMedicines.length,
    };
  }

  Future<List<AppointmentInfo>> getUpcomingAppointments() async {
    List<AppointmentInfo> appointments = [];
    final now = DateTime.now();
    
    try {
      for (final member in _familyMembers) {
        final visits = await _databaseHelper.getDoctorVisits(member.id!);
        for (final visit in visits) {
          final visitDate = DateTime.tryParse(visit.visitDate);
          if (visitDate != null && visitDate.isAfter(now)) {
            appointments.add(AppointmentInfo(
              familyMember: member,
              doctorVisit: visit,
            ));
          }
        }
      }
      
      // Sort by date
      appointments.sort((a, b) {
        final dateA = DateTime.tryParse(a.doctorVisit.visitDate) ?? DateTime.now();
        final dateB = DateTime.tryParse(b.doctorVisit.visitDate) ?? DateTime.now();
        return dateA.compareTo(dateB);
      });
      
      return appointments.take(5).toList(); // Return next 5 appointments
    } catch (e) {
      _setError('Failed to load upcoming appointments: ${e.toString()}');
      return [];
    }
  }

  List<Medicine> getMedicinesEndingSoon() {
    final now = DateTime.now();
    final sevenDaysFromNow = now.add(const Duration(days: 7));
    
    return _activeMedicines.where((medicine) {
      return medicine.endDate.isAfter(now) && 
             medicine.endDate.isBefore(sevenDaysFromNow);
    }).toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}

class AppointmentInfo {
  final FamilyMember familyMember;
  final DoctorVisit doctorVisit;

  AppointmentInfo({
    required this.familyMember,
    required this.doctorVisit,
  });
}
