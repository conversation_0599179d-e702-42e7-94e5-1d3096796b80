
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/widgets/date_picker_field.dart';

class AddDoctorVisitScreen extends StatefulWidget {
  final int familyMemberId;

  const AddDoctorVisitScreen({super.key, required this.familyMemberId});

  @override
  State<AddDoctorVisitScreen> createState() => _AddDoctorVisitScreenState();
}

class _AddDoctorVisitScreenState extends State<AddDoctorVisitScreen> {
  final _formKey = GlobalKey<FormState>();
  final _doctorNameController = TextEditingController();
  final _visitDateController = TextEditingController();
  final _reasonController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Doctor Visit'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _doctorNameController,
                decoration: const InputDecoration(labelText: 'Doctor Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a doctor name';
                  }
                  return null;
                },
              ),
              DatePickerField(
                controller: _visitDateController,
                labelText: 'Visit Date',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a visit date';
                  }
                  return null;
                },
                lastDate: DateTime.now(),
              ),
              TextFormField(
                controller: _reasonController,
                decoration: const InputDecoration(labelText: 'Reason for Visit'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a reason for the visit';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(labelText: 'Notes'),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    final newVisit = DoctorVisit(
                      familyMemberId: widget.familyMemberId,
                      doctorName: _doctorNameController.text,
                      visitDate: _visitDateController.text,
                      reason: _reasonController.text,
                      notes: _notesController.text,
                    );
                    DatabaseHelper().insertDoctorVisit(newVisit);
                    Navigator.pop(context);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
