import 'package:flutter/foundation.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';

class FamilyProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  List<FamilyMember> _familyMembers = [];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';

  List<FamilyMember> get familyMembers => _filteredFamilyMembers;
  List<FamilyMember> get allFamilyMembers => _familyMembers;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;

  List<FamilyMember> get _filteredFamilyMembers {
    if (_searchQuery.isEmpty) {
      return _familyMembers;
    }
    return _familyMembers.where((member) {
      return member.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             member.relation.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  Future<void> loadFamilyMembers() async {
    _setLoading(true);
    _setError(null);
    
    try {
      _familyMembers = await _databaseHelper.getFamilyMembers();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load family members: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addFamilyMember(FamilyMember member) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.insertFamilyMember(member);
      await loadFamilyMembers(); // Reload the list
    } catch (e) {
      _setError('Failed to add family member: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  Future<void> updateFamilyMember(FamilyMember member) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.updateFamilyMember(member);
      await loadFamilyMembers(); // Reload the list
    } catch (e) {
      _setError('Failed to update family member: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  Future<void> deleteFamilyMember(int id) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.deleteFamilyMember(id);
      await loadFamilyMembers(); // Reload the list
    } catch (e) {
      _setError('Failed to delete family member: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void clearSearch() {
    _searchQuery = '';
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
