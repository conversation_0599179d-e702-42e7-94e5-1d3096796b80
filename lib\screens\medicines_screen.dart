
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_medicine_screen.dart';
import 'package:medical/screens/medicine_reminders_screen.dart';

class MedicinesScreen extends StatefulWidget {
  final DoctorVisit visit;

  const MedicinesScreen({super.key, required this.visit});

  @override
  State<MedicinesScreen> createState() => _MedicinesScreenState();
}

class _MedicinesScreenState extends State<MedicinesScreen> {
  late Future<List<Medicine>> _medicines;
  final TextEditingController _searchController = TextEditingController();
  List<Medicine> _allMedicines = [];
  List<Medicine> _filteredMedicines = [];

  @override
  void initState() {
    super.initState();
    _loadMedicines();
    _searchController.addListener(_filterMedicines);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadMedicines() async {
    final medicines = await <PERSON>Helper().getMedicines(widget.visit.id!);
    setState(() {
      _allMedicines = medicines;
      _filteredMedicines = medicines;
      _medicines = Future.value(medicines);
    });
  }

  void _filterMedicines() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredMedicines = _allMedicines;
      } else {
        _filteredMedicines = _allMedicines.where((medicine) {
          return medicine.medicineName.toLowerCase().contains(query) ||
                 medicine.dosage.toLowerCase().contains(query) ||
                 medicine.frequency.toLowerCase().contains(query);
        }).toList();
      }
      _medicines = Future.value(_filteredMedicines);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Medicines for ${widget.visit.visitDate}'),
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search medicines...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          // Medicines List
          Expanded(
            child: FutureBuilder<List<Medicine>>(
              future: _medicines,
              builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No medicines found.'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                final medicine = snapshot.data![index];
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                  child: ListTile(
                    title: Text(medicine.medicineName),
                    subtitle: Text('Dosage: ${medicine.dosage} - Frequency: ${medicine.frequency}'),
                    trailing: IconButton(
                      icon: const Icon(Icons.alarm, color: Colors.blue),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MedicineRemindersScreen(medicine: medicine),
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
            );
          }
        },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddMedicineScreen(visitId: widget.visit.id!),
            ),
          );
          _loadMedicines();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
