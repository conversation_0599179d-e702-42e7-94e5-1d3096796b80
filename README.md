# MediKeep - Medical Records Management App

A comprehensive Flutter application for managing family medical records, appointments, medicines, and health data with a focus on mobile and web responsiveness.

## 🌟 Features

### ✅ **Implemented Features**

#### **Core Functionality**
- **Family Member Management**: Add, edit, delete, and search family members
- **Doctor Visits Tracking**: Record and manage doctor appointments with detailed information
- **Medical Diagnoses**: Track diagnoses for each doctor visit
- **Medicine Management**: Manage medications with dosage, frequency, and duration
- **Medical Reports**: Store and view medical reports and documents
- **Medicine Reminders**: Set up notifications for medicine schedules

#### **User Experience**
- **Responsive Design**: Optimized for mobile, tablet, and desktop/web
- **Search & Filter**: Real-time search across all data types
- **Interactive Dashboard**: Overview of medical statistics and active medicines
- **Date Pickers**: User-friendly date selection for all date inputs
- **File Viewing**: Preview medical report images with zoom/pan functionality

#### **Data Management**
- **Local Database**: SQLite database for secure local data storage
- **CRUD Operations**: Complete Create, Read, Update, Delete for all entities
- **Data Validation**: Comprehensive input validation and error handling
- **Export Functionality**: Export data to CSV format (PDF export ready for additional packages)

#### **Technical Features**
- **Provider State Management**: Efficient state management using Provider pattern
- **Notification System**: Local notifications for medicine reminders
- **Responsive Widgets**: Custom responsive components for different screen sizes
- **Error Handling**: Comprehensive error handling and user feedback

### 🚧 **Ready for Enhancement**
- **PDF Export**: Requires `pdf` and `printing` packages
- **Data Import**: Requires `file_picker` and `csv` packages
- **File Sharing**: Requires `share_plus` package

## 📱 **Responsive Design**

The app is designed to work seamlessly across different platforms:

- **Mobile (< 768px)**: Bottom navigation, optimized touch interactions
- **Tablet (768px - 1200px)**: Navigation rail, larger content areas
- **Desktop/Web (> 1200px)**: Full navigation rail, maximum content width constraints

## 🏗️ **Architecture**

### **Project Structure**
```
lib/
├── database/           # SQLite database helper
├── models/            # Data models (FamilyMember, DoctorVisit, etc.)
├── providers/         # State management providers
├── screens/           # UI screens
├── services/          # Business logic services
├── utils/            # Utilities (validators, responsive helpers)
└── widgets/          # Reusable UI components
```

### **Database Schema**
- **family_members**: Basic family member information
- **doctor_visits**: Medical appointments and visits
- **diagnoses**: Medical diagnoses linked to visits
- **medicines**: Medication records with schedules
- **medical_reports**: File storage for medical documents
- **reminders**: Medicine reminder notifications

### **State Management**
Uses Provider pattern with dedicated providers for:
- `FamilyProvider`: Family member management
- `DoctorVisitsProvider`: Doctor visits and appointments
- `MedicinesProvider`: Medicine and reminder management
- `DashboardProvider`: Dashboard statistics and overview

## 🚀 **Getting Started**

### **Prerequisites**
- Flutter SDK (3.0 or higher)
- Dart SDK
- Android Studio / VS Code
- For web: Chrome browser

### **Installation**
1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```
3. Run the app:
   ```bash
   # For mobile
   flutter run

   # For web
   flutter run -d chrome
   ```

### **Optional Packages**
To enable full export/import functionality, add these packages to `pubspec.yaml`:
```yaml
dependencies:
  pdf: ^3.10.7
  printing: ^5.12.0
  csv: ^6.0.0
  share_plus: ^7.2.2
  file_picker: ^6.1.1
```

## 📊 **Database Features**

### **Data Models**
- **FamilyMember**: Name, date of birth, relation
- **DoctorVisit**: Doctor name, visit date, reason, notes
- **Diagnosis**: Diagnosis text, date
- **Medicine**: Name, dosage, frequency, start/end dates
- **MedicalReport**: Report name, file path
- **Reminder**: Medicine reminders with notification settings

### **Relationships**
- Family Members → Doctor Visits (1:many)
- Doctor Visits → Diagnoses (1:many)
- Doctor Visits → Medicines (1:many)
- Doctor Visits → Medical Reports (1:many)
- Medicines → Reminders (1:many)

## 🎨 **UI/UX Features**

### **Responsive Components**
- `ResponsiveWidget`: Adaptive layouts for different screen sizes
- `ResponsiveContainer`: Content width constraints
- `ResponsiveGridView`: Adaptive grid layouts

### **Custom Widgets**
- `DatePickerField`: Interactive date selection
- `SearchBarWidget`: Consistent search interface
- `CustomCard` & `InfoCard`: Reusable card components
- `EmptyStateWidget`: User-friendly empty states
- `LoadingWidget`: Consistent loading indicators

### **Navigation**
- **Mobile**: Bottom navigation bar
- **Tablet/Desktop**: Navigation rail with labels
- **Responsive**: Automatically adapts based on screen size

## 🔔 **Notification System**

### **Medicine Reminders**
- Set custom reminder times for each medicine
- Enable/disable reminders individually
- Automatic scheduling with `flutter_local_notifications`
- Persistent reminders until medicine end date

### **Notification Features**
- Daily recurring reminders
- Custom notification content
- Tap handling for future enhancements
- Automatic cleanup when medicines end

## 📈 **Dashboard Analytics**

### **Statistics Tracking**
- Total family members
- Total doctor visits
- Total diagnoses
- Total medical reports
- Active medicines count

### **Quick Access**
- Recent family members
- Active medicines with days remaining
- Upcoming appointments (when available)
- Medicine ending soon alerts

## 🔒 **Privacy & Security**

- **Local Storage**: All data stored locally on device
- **No Cloud Sync**: Complete privacy with local-only data
- **Secure Database**: SQLite with proper data validation
- **Input Validation**: Comprehensive validation for all user inputs

## 🛠️ **Development**

### **Code Quality**
- Comprehensive input validation
- Error handling with user feedback
- Consistent code structure
- Responsive design patterns

### **Testing**
- Unit tests for validators
- Widget tests for UI components
- Integration tests for database operations

### **Future Enhancements**
- Cloud backup and sync
- Multi-language support
- Advanced analytics and insights
- Integration with health APIs
- Appointment scheduling with calendar sync

## 📄 **License**

This project is private and not intended for public distribution.

## 🤝 **Contributing**

This is a private medical records application. For feature requests or bug reports, please contact the development team.

---

**MediKeep** - Your comprehensive medical records companion 🏥📱
