import 'package:flutter/material.dart';
import 'package:medical/services/navigation_service.dart';
import 'package:medical/utils/responsive.dart';
import 'package:medical/models/models.dart';

class ContextualNavigationBar extends StatelessWidget {
  final String currentContext;
  final List<ContextualAction> actions;
  final Widget? customContent;

  const ContextualNavigationBar({
    super.key,
    required this.currentContext,
    this.actions = const [],
    this.customContent,
  });

  @override
  Widget build(BuildContext context) {
    if (actions.isEmpty && customContent == null) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: Responsive.isMobile(context) ? 16.0 : 24.0,
        vertical: 12.0,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: customContent ?? _buildDefaultContent(context),
    );
  }

  Widget _buildDefaultContent(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.info_outline,
          size: 16,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            currentContext,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
        if (actions.isNotEmpty) ...[
          const SizedBox(width: 16),
          ...actions.map((action) => Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: _buildActionButton(context, action),
          )),
        ],
      ],
    );
  }

  Widget _buildActionButton(BuildContext context, ContextualAction action) {
    if (Responsive.isMobile(context)) {
      return IconButton(
        onPressed: action.onPressed,
        icon: Icon(action.icon, size: 18),
        tooltip: action.label,
        padding: const EdgeInsets.all(4),
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
      );
    }

    return TextButton.icon(
      onPressed: action.onPressed,
      icon: Icon(action.icon, size: 16),
      label: Text(action.label),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        textStyle: const TextStyle(fontSize: 12),
      ),
    );
  }
}

class ContextualAction {
  final String label;
  final IconData icon;
  final VoidCallback onPressed;
  final Color? color;

  ContextualAction({
    required this.label,
    required this.icon,
    required this.onPressed,
    this.color,
  });
}

// Specific contextual navigation widgets for different screens
class FamilyMemberContextualNav extends StatelessWidget {
  final FamilyMember familyMember;

  const FamilyMemberContextualNav({
    super.key,
    required this.familyMember,
  });

  @override
  Widget build(BuildContext context) {
    return ContextualNavigationBar(
      currentContext: 'Viewing ${familyMember.name}\'s medical records',
      actions: [
        ContextualAction(
          label: 'Add Visit',
          icon: Icons.add_circle,
          onPressed: () => NavigationService().addDoctorVisit(familyMember.id!),
        ),
        ContextualAction(
          label: 'Edit Member',
          icon: Icons.edit,
          onPressed: () => NavigationService().addFamilyMember(familyMember),
        ),
      ],
    );
  }
}

class DoctorVisitContextualNav extends StatelessWidget {
  final DoctorVisit visit;

  const DoctorVisitContextualNav({
    super.key,
    required this.visit,
  });

  @override
  Widget build(BuildContext context) {
    return ContextualNavigationBar(
      currentContext: 'Visit with ${visit.doctorName} on ${visit.visitDate}',
      actions: [
        ContextualAction(
          label: 'Add Diagnosis',
          icon: Icons.medical_services,
          onPressed: () => NavigationService().addDiagnosis(visit.id!),
        ),
        ContextualAction(
          label: 'Add Medicine',
          icon: Icons.medication,
          onPressed: () => NavigationService().addMedicine(visit.id!),
        ),
        ContextualAction(
          label: 'Add Report',
          icon: Icons.receipt_long,
          onPressed: () => NavigationService().addMedicalReport(visit.id!),
        ),
      ],
    );
  }
}

class MedicineContextualNav extends StatelessWidget {
  final Medicine medicine;

  const MedicineContextualNav({
    super.key,
    required this.medicine,
  });

  @override
  Widget build(BuildContext context) {
    return ContextualNavigationBar(
      currentContext: 'Managing ${medicine.medicineName} (${medicine.dosage})',
      actions: [
        ContextualAction(
          label: 'Add Reminder',
          icon: Icons.alarm_add,
          onPressed: () => NavigationService().addReminder(medicine.id!),
        ),
        ContextualAction(
          label: 'Edit Medicine',
          icon: Icons.edit,
          onPressed: () => NavigationService().addMedicine(medicine.visitId, medicine),
        ),
      ],
    );
  }
}

// Smart navigation suggestions based on current context
class SmartNavigationSuggestions extends StatelessWidget {
  final String currentScreen;
  final Map<String, dynamic>? contextData;

  const SmartNavigationSuggestions({
    super.key,
    required this.currentScreen,
    this.contextData,
  });

  @override
  Widget build(BuildContext context) {
    final suggestions = _getSuggestionsForScreen(currentScreen, contextData);
    
    if (suggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Quick Actions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: suggestions.map((suggestion) => _buildSuggestionChip(context, suggestion)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionChip(BuildContext context, NavigationSuggestion suggestion) {
    return ActionChip(
      avatar: Icon(suggestion.icon, size: 16),
      label: Text(suggestion.label),
      onPressed: suggestion.onPressed,
      backgroundColor: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
      side: BorderSide.none,
    );
  }

  List<NavigationSuggestion> _getSuggestionsForScreen(String screen, Map<String, dynamic>? data) {
    switch (screen) {
      case 'dashboard':
        return [
          NavigationSuggestion(
            label: 'Add Family Member',
            icon: Icons.person_add,
            onPressed: () => NavigationService().addFamilyMember(),
          ),
          NavigationSuggestion(
            label: 'View Family',
            icon: Icons.people,
            onPressed: () => NavigationService().goToFamily(),
          ),
        ];
      
      case 'family':
        return [
          NavigationSuggestion(
            label: 'Add Member',
            icon: Icons.person_add,
            onPressed: () => NavigationService().addFamilyMember(),
          ),
          NavigationSuggestion(
            label: 'Schedule Appointment',
            icon: Icons.calendar_today,
            onPressed: () => NavigationService().goToAppointments(),
          ),
        ];
      
      case 'appointments':
        return [
          NavigationSuggestion(
            label: 'Add Appointment',
            icon: Icons.add_circle,
            onPressed: () => _showAppointmentDialog(),
          ),
          NavigationSuggestion(
            label: 'View Family',
            icon: Icons.people,
            onPressed: () => NavigationService().goToFamily(),
          ),
        ];
      
      case 'doctor_visits':
        if (data != null && data['familyMember'] != null) {
          final member = data['familyMember'] as FamilyMember;
          return [
            NavigationSuggestion(
              label: 'Add Visit',
              icon: Icons.add_circle,
              onPressed: () => NavigationService().addDoctorVisit(member.id!),
            ),
            NavigationSuggestion(
              label: 'Edit ${member.name}',
              icon: Icons.edit,
              onPressed: () => NavigationService().addFamilyMember(member),
            ),
          ];
        }
        break;
      
      case 'medicines':
        if (data != null && data['visit'] != null) {
          final visit = data['visit'] as DoctorVisit;
          return [
            NavigationSuggestion(
              label: 'Add Medicine',
              icon: Icons.medication,
              onPressed: () => NavigationService().addMedicine(visit.id!),
            ),
            NavigationSuggestion(
              label: 'Add Report',
              icon: Icons.receipt_long,
              onPressed: () => NavigationService().addMedicalReport(visit.id!),
            ),
          ];
        }
        break;
    }
    
    return [];
  }

  void _showAppointmentDialog() {
    // Implementation would be similar to other dialogs
  }
}

class NavigationSuggestion {
  final String label;
  final IconData icon;
  final VoidCallback onPressed;

  NavigationSuggestion({
    required this.label,
    required this.icon,
    required this.onPressed,
  });
}

// Workflow progress indicator
class WorkflowProgressIndicator extends StatelessWidget {
  final List<WorkflowStep> steps;
  final int currentStep;

  const WorkflowProgressIndicator({
    super.key,
    required this.steps,
    required this.currentStep,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Workflow Progress',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: steps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isActive = index == currentStep;
              final isCompleted = index < currentStep;
              
              return Expanded(
                child: Row(
                  children: [
                    _buildStepIndicator(context, step, isActive, isCompleted),
                    if (index < steps.length - 1)
                      Expanded(
                        child: Container(
                          height: 2,
                          color: isCompleted
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                        ),
                      ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(BuildContext context, WorkflowStep step, bool isActive, bool isCompleted) {
    return Column(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isCompleted
                ? Theme.of(context).colorScheme.primary
                : isActive
                    ? Theme.of(context).colorScheme.primaryContainer
                    : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
          child: Icon(
            isCompleted ? Icons.check : step.icon,
            size: 16,
            color: isCompleted || isActive
                ? Theme.of(context).colorScheme.onPrimary
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          step.label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
            color: isActive
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class WorkflowStep {
  final String label;
  final IconData icon;

  WorkflowStep({
    required this.label,
    required this.icon,
  });
}
