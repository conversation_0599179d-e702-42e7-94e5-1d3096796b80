class Validators {
  // Name validation
  static String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters long';
    }
    if (value.trim().length > 50) {
      return 'Name must be less than 50 characters';
    }
    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(value.trim())) {
      return 'Name can only contain letters and spaces';
    }
    return null;
  }

  // Date validation
  static String? validateDate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Date is required';
    }
    
    try {
      final date = DateTime.parse(value);
      final now = DateTime.now();
      
      // Check if date is not in the future (for birth dates, visit dates, etc.)
      if (date.isAfter(now)) {
        return 'Date cannot be in the future';
      }
      
      // Check if date is not too far in the past (reasonable birth date)
      final hundredYearsAgo = DateTime(now.year - 100, now.month, now.day);
      if (date.isBefore(hundredYearsAgo)) {
        return 'Date cannot be more than 100 years ago';
      }
      
      return null;
    } catch (e) {
      return 'Please enter a valid date';
    }
  }

  // Future date validation (for medicine end dates, appointments)
  static String? validateFutureDate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Date is required';
    }
    
    try {
      final date = DateTime.parse(value);
      final now = DateTime.now();
      
      // Allow current date and future dates
      if (date.isBefore(DateTime(now.year, now.month, now.day))) {
        return 'Date cannot be in the past';
      }
      
      return null;
    } catch (e) {
      return 'Please enter a valid date';
    }
  }

  // Relation validation
  static String? validateRelation(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Relation is required';
    }
    if (value.trim().length < 2) {
      return 'Relation must be at least 2 characters long';
    }
    if (value.trim().length > 30) {
      return 'Relation must be less than 30 characters';
    }
    return null;
  }

  // Doctor name validation
  static String? validateDoctorName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Doctor name is required';
    }
    if (value.trim().length < 2) {
      return 'Doctor name must be at least 2 characters long';
    }
    if (value.trim().length > 100) {
      return 'Doctor name must be less than 100 characters';
    }
    return null;
  }

  // Reason validation
  static String? validateReason(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Reason is required';
    }
    if (value.trim().length < 3) {
      return 'Reason must be at least 3 characters long';
    }
    if (value.trim().length > 200) {
      return 'Reason must be less than 200 characters';
    }
    return null;
  }

  // Diagnosis validation
  static String? validateDiagnosis(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Diagnosis is required';
    }
    if (value.trim().length < 3) {
      return 'Diagnosis must be at least 3 characters long';
    }
    if (value.trim().length > 500) {
      return 'Diagnosis must be less than 500 characters';
    }
    return null;
  }

  // Medicine name validation
  static String? validateMedicineName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Medicine name is required';
    }
    if (value.trim().length < 2) {
      return 'Medicine name must be at least 2 characters long';
    }
    if (value.trim().length > 100) {
      return 'Medicine name must be less than 100 characters';
    }
    return null;
  }

  // Dosage validation
  static String? validateDosage(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Dosage is required';
    }
    if (value.trim().length < 2) {
      return 'Dosage must be at least 2 characters long';
    }
    if (value.trim().length > 50) {
      return 'Dosage must be less than 50 characters';
    }
    return null;
  }

  // Frequency validation
  static String? validateFrequency(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Frequency is required';
    }
    if (value.trim().length < 2) {
      return 'Frequency must be at least 2 characters long';
    }
    if (value.trim().length > 50) {
      return 'Frequency must be less than 50 characters';
    }
    return null;
  }

  // Report name validation
  static String? validateReportName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Report name is required';
    }
    if (value.trim().length < 3) {
      return 'Report name must be at least 3 characters long';
    }
    if (value.trim().length > 100) {
      return 'Report name must be less than 100 characters';
    }
    return null;
  }

  // Date range validation (start date should be before end date)
  static String? validateDateRange(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) {
      return null; // Individual date validation will handle null values
    }
    
    try {
      final start = DateTime.parse(startDate);
      final end = DateTime.parse(endDate);
      
      if (start.isAfter(end)) {
        return 'Start date must be before end date';
      }
      
      return null;
    } catch (e) {
      return null; // Individual date validation will handle invalid dates
    }
  }

  // Notes validation (optional field)
  static String? validateNotes(String? value) {
    if (value != null && value.trim().length > 1000) {
      return 'Notes must be less than 1000 characters';
    }
    return null;
  }
}
