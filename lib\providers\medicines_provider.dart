import 'package:flutter/foundation.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';

class MedicinesProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  List<Medicine> _medicines = [];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  int? _currentVisitId;

  List<Medicine> get medicines => _filteredMedicines;
  List<Medicine> get allMedicines => _medicines;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;

  List<Medicine> get _filteredMedicines {
    if (_searchQuery.isEmpty) {
      return _medicines;
    }
    return _medicines.where((medicine) {
      return medicine.medicineName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             medicine.dosage.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             medicine.frequency.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  List<Medicine> get activeMedicines {
    final now = DateTime.now();
    return _medicines.where((medicine) => medicine.endDate.isAfter(now)).toList();
  }

  Future<void> loadMedicines(int visitId) async {
    _currentVisitId = visitId;
    _setLoading(true);
    _setError(null);
    
    try {
      _medicines = await _databaseHelper.getMedicines(visitId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load medicines: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<List<Medicine>> getAllActiveMedicines() async {
    try {
      final familyMembers = await _databaseHelper.getFamilyMembers();
      List<Medicine> allMedicines = [];
      
      for (final member in familyMembers) {
        final visits = await _databaseHelper.getDoctorVisits(member.id!);
        for (final visit in visits) {
          final medicines = await _databaseHelper.getMedicines(visit.id!);
          final activeMedicines = medicines.where((medicine) => 
            medicine.endDate.isAfter(DateTime.now())
          ).toList();
          allMedicines.addAll(activeMedicines);
        }
      }
      
      return allMedicines;
    } catch (e) {
      _setError('Failed to load active medicines: ${e.toString()}');
      return [];
    }
  }

  Future<void> addMedicine(Medicine medicine) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.insertMedicine(medicine);
      if (_currentVisitId != null) {
        await loadMedicines(_currentVisitId!);
      }
    } catch (e) {
      _setError('Failed to add medicine: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  Future<void> updateMedicine(Medicine medicine) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.updateMedicine(medicine);
      if (_currentVisitId != null) {
        await loadMedicines(_currentVisitId!);
      }
    } catch (e) {
      _setError('Failed to update medicine: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  Future<void> deleteMedicine(int id) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.deleteMedicine(id);
      if (_currentVisitId != null) {
        await loadMedicines(_currentVisitId!);
      }
    } catch (e) {
      _setError('Failed to delete medicine: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void clearSearch() {
    _searchQuery = '';
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
