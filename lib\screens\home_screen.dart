
import 'package:flutter/material.dart';
import 'package:medical/screens/dashboard_screen.dart';
import 'package:medical/screens/family_members_screen.dart';
import 'package:medical/screens/appointments_screen.dart';
import 'package:medical/screens/settings_screen.dart';
import 'package:medical/utils/responsive.dart';
import 'package:medical/widgets/breadcrumb_navigation.dart';
import 'package:medical/widgets/quick_action_menu.dart';
import 'package:medical/widgets/navigation_drawer.dart';
import 'package:medical/widgets/global_search.dart';
import 'package:medical/services/navigation_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  static const List<Widget> _widgetOptions = <Widget>[
    DashboardScreen(),
    FamilyMembersScreen(),
    AppointmentsScreen(),
    SettingsScreen(),
  ];

  // Enhanced navigation items with better icons and descriptions
  static const List<_NavigationItemData> _navigationItems = [
    _NavigationItemData(
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard,
      label: 'Dashboard',
      tooltip: 'Overview of medical data',
    ),
    _NavigationItemData(
      icon: Icons.people_outline,
      selectedIcon: Icons.people,
      label: 'Family',
      tooltip: 'Manage family members',
    ),
    _NavigationItemData(
      icon: Icons.calendar_today_outlined,
      selectedIcon: Icons.calendar_today,
      label: 'Appointments',
      tooltip: 'Doctor visits and appointments',
    ),
    _NavigationItemData(
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings,
      label: 'Settings',
      tooltip: 'App settings and data management',
    ),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });

    // Clear navigation stack when switching main tabs
    NavigationService().clearNavigationStack();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_navigationItems[_selectedIndex].label),
        centerTitle: true,
        actions: const [
          GlobalSearchButton(),
        ],
      ),
      drawer: Responsive.isMobile(context) ? const MedicalNavigationDrawer() : null,
      body: Column(
        children: [
          // Breadcrumb navigation for larger screens
          if (!Responsive.isMobile(context))
            const BreadcrumbNavigation(),

          // Quick access bar for tablets/desktop
          if (!Responsive.isMobile(context))
            const QuickAccessBar(),

          // Main content area
          Expanded(
            child: ResponsiveWidget(
              mobile: _widgetOptions.elementAt(_selectedIndex),
              tablet: Row(
                children: [
                  _buildNavigationRail(context),
                  const VerticalDivider(thickness: 1, width: 1),
                  Expanded(
                    child: _widgetOptions.elementAt(_selectedIndex),
                  ),
                ],
              ),
              desktop: Row(
                children: [
                  _buildNavigationRail(context, extended: true),
                  const VerticalDivider(thickness: 1, width: 1),
                  Expanded(
                    child: _widgetOptions.elementAt(_selectedIndex),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Responsive.isMobile(context)
          ? _buildBottomNavigationBar(context)
          : null,
      floatingActionButton: const QuickActionMenu(),
    );
  }

  Widget _buildNavigationRail(BuildContext context, {bool extended = false}) {
    return NavigationRail(
      selectedIndex: _selectedIndex,
      onDestinationSelected: _onItemTapped,
      labelType: extended
          ? NavigationRailLabelType.all
          : NavigationRailLabelType.selected,
      extended: extended && Responsive.isDesktop(context),
      destinations: _navigationItems.map((item) => NavigationRailDestination(
        icon: Icon(item.icon),
        selectedIcon: Icon(item.selectedIcon),
        label: Text(item.label),
      )).toList(),
      backgroundColor: Theme.of(context).colorScheme.surface,
      elevation: 1,
    );
  }

  Widget _buildBottomNavigationBar(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      items: _navigationItems.map((item) => BottomNavigationBarItem(
        icon: Icon(item.icon),
        activeIcon: Icon(item.selectedIcon),
        label: item.label,
      )).toList(),
      currentIndex: _selectedIndex,
      selectedItemColor: Theme.of(context).colorScheme.primary,
      unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
      onTap: _onItemTapped,
      elevation: 8,
      backgroundColor: Theme.of(context).colorScheme.surface,
    );
  }
}

class _NavigationItemData {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final String tooltip;

  const _NavigationItemData({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    required this.tooltip,
  });
}
