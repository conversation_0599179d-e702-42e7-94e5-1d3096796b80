
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_medical_report_screen.dart';
import 'package:medical/screens/medical_report_viewer_screen.dart';

class MedicalReportsScreen extends StatefulWidget {
  final DoctorVisit visit;

  const MedicalReportsScreen({super.key, required this.visit});

  @override
  State<MedicalReportsScreen> createState() => _MedicalReportsScreenState();
}

class _MedicalReportsScreenState extends State<MedicalReportsScreen> {
  late Future<List<MedicalReport>> _medicalReports;

  @override
  void initState() {
    super.initState();
    _loadMedicalReports();
  }

  void _loadMedicalReports() {
    setState(() {
      _medicalReports = DatabaseHelper().getMedicalReports(widget.visit.id!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Medical Reports for ${widget.visit.visitDate}'),
      ),
      body: FutureBuilder<List<MedicalReport>>(
        future: _medicalReports,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No medical reports found.'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                final report = snapshot.data![index];
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                  child: ListTile(
                    leading: const Icon(Icons.description, color: Colors.blue),
                    title: Text(report.reportName),
                    subtitle: Text(_getFileInfo(report.reportPath)),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => MedicalReportViewerScreen(report: report),
                        ),
                      );
                    },
                  ),
                );
              },
            );
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddMedicalReportScreen(visitId: widget.visit.id!),
            ),
          );
          _loadMedicalReports();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  String _getFileInfo(String filePath) {
    final fileName = filePath.split('/').last;
    final extension = fileName.split('.').last.toUpperCase();
    return '$extension file';
  }
}
