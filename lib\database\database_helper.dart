
import 'package:medical/models/models.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:async';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    String path = join(await getDatabasesPath(), 'medical_records.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE family_members(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        dateOfBirth TEXT NOT NULL,
        relation TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE doctor_visits(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        familyMemberId INTEGER NOT NULL,
        doctorName TEXT NOT NULL,
        visitDate TEXT NOT NULL,
        reason TEXT,
        notes TEXT,
        FOREIGN KEY (familyMemberId) REFERENCES family_members (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE diagnoses(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        visitId INTEGER NOT NULL,
        diagnosis TEXT NOT NULL,
        date TEXT NOT NULL,
        FOREIGN KEY (visitId) REFERENCES doctor_visits (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE medical_reports(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        visitId INTEGER NOT NULL,
        reportPath TEXT NOT NULL,
        reportName TEXT NOT NULL,
        FOREIGN KEY (visitId) REFERENCES doctor_visits (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE medicines(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        visitId INTEGER NOT NULL,
        medicineName TEXT NOT NULL,
        dosage TEXT NOT NULL,
        frequency TEXT NOT NULL,
        startDate TEXT NOT NULL,
        endDate TEXT NOT NULL,
        FOREIGN KEY (visitId) REFERENCES doctor_visits (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE reminders(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        medicineId INTEGER NOT NULL,
        reminderTime TEXT NOT NULL,
        enabled INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (medicineId) REFERENCES medicines (id) ON DELETE CASCADE
      )
    ''');
  }

  Future<int> insertFamilyMember(FamilyMember member) async {
    final db = await database;
    return await db.insert('family_members', member.toMap());
  }

  Future<List<FamilyMember>> getFamilyMembers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('family_members');
    return List.generate(maps.length, (i) {
      return FamilyMember.fromMap(maps[i]);
    });
  }

  Future<int> updateFamilyMember(FamilyMember member) async {
    final db = await database;
    return await db.update(
      'family_members',
      member.toMap(),
      where: 'id = ?',
      whereArgs: [member.id],
    );
  }

  Future<int> deleteFamilyMember(int id) async {
    final db = await database;
    return await db.delete(
      'family_members',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> insertDoctorVisit(DoctorVisit visit) async {
    final db = await database;
    return await db.insert('doctor_visits', visit.toMap());
  }

  Future<List<DoctorVisit>> getDoctorVisits(int familyMemberId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'doctor_visits',
      where: 'familyMemberId = ?',
      whereArgs: [familyMemberId],
    );
    return List.generate(maps.length, (i) {
      return DoctorVisit.fromMap(maps[i]);
    });
  }

  Future<int> updateDoctorVisit(DoctorVisit visit) async {
    final db = await database;
    return await db.update(
      'doctor_visits',
      visit.toMap(),
      where: 'id = ?',
      whereArgs: [visit.id],
    );
  }

  Future<int> deleteDoctorVisit(int id) async {
    final db = await database;
    return await db.delete(
      'doctor_visits',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> insertDiagnosis(Diagnosis diagnosis) async {
    final db = await database;
    return await db.insert('diagnoses', diagnosis.toMap());
  }

  Future<List<Diagnosis>> getDiagnoses(int visitId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'diagnoses',
      where: 'visitId = ?',
      whereArgs: [visitId],
    );
    return List.generate(maps.length, (i) {
      return Diagnosis.fromMap(maps[i]);
    });
  }

  Future<int> updateDiagnosis(Diagnosis diagnosis) async {
    final db = await database;
    return await db.update(
      'diagnoses',
      diagnosis.toMap(),
      where: 'id = ?',
      whereArgs: [diagnosis.id],
    );
  }

  Future<int> deleteDiagnosis(int id) async {
    final db = await database;
    return await db.delete(
      'diagnoses',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> insertMedicalReport(MedicalReport report) async {
    final db = await database;
    return await db.insert('medical_reports', report.toMap());
  }

  Future<List<MedicalReport>> getMedicalReports(int visitId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'medical_reports',
      where: 'visitId = ?',
      whereArgs: [visitId],
    );
    return List.generate(maps.length, (i) {
      return MedicalReport.fromMap(maps[i]);
    });
  }

  Future<int> updateMedicalReport(MedicalReport report) async {
    final db = await database;
    return await db.update(
      'medical_reports',
      report.toMap(),
      where: 'id = ?',
      whereArgs: [report.id],
    );
  }

  Future<int> deleteMedicalReport(int id) async {
    final db = await database;
    return await db.delete(
      'medical_reports',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> insertMedicine(Medicine medicine) async {
    final db = await database;
    return await db.insert('medicines', medicine.toMap());
  }

  Future<List<Medicine>> getMedicines(int visitId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'medicines',
      where: 'visitId = ?',
      whereArgs: [visitId],
    );
    return List.generate(maps.length, (i) {
      return Medicine.fromMap(maps[i]);
    });
  }

  Future<int> updateMedicine(Medicine medicine) async {
    final db = await database;
    return await db.update(
      'medicines',
      medicine.toMap(),
      where: 'id = ?',
      whereArgs: [medicine.id],
    );
  }

  Future<int> deleteMedicine(int id) async {
    final db = await database;
    return await db.delete(
      'medicines',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Reminder CRUD operations
  Future<int> insertReminder(Reminder reminder) async {
    final db = await database;
    return await db.insert('reminders', reminder.toMap());
  }

  Future<List<Reminder>> getReminders(int medicineId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'reminders',
      where: 'medicineId = ?',
      whereArgs: [medicineId],
    );
    return List.generate(maps.length, (i) {
      return Reminder.fromMap(maps[i]);
    });
  }

  Future<List<Reminder>> getAllActiveReminders() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'reminders',
      where: 'enabled = ?',
      whereArgs: [1],
    );
    return List.generate(maps.length, (i) {
      return Reminder.fromMap(maps[i]);
    });
  }

  Future<int> updateReminder(Reminder reminder) async {
    final db = await database;
    return await db.update(
      'reminders',
      reminder.toMap(),
      where: 'id = ?',
      whereArgs: [reminder.id],
    );
  }

  Future<int> deleteReminder(int id) async {
    final db = await database;
    return await db.delete(
      'reminders',
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
