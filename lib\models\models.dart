
class FamilyMember {
  int? id;
  String name;
  String dateOfBirth;
  String relation;

  FamilyMember({this.id, required this.name, required this.dateOfBirth, required this.relation});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'dateOfBirth': dateOfBirth,
      'relation': relation,
    };
  }

  factory FamilyMember.fromMap(Map<String, dynamic> map) {
    return FamilyMember(
      id: map['id'],
      name: map['name'],
      dateOfBirth: map['dateOfBirth'],
      relation: map['relation'],
    );
  }
}

class DoctorVisit {
  int? id;
  int familyMemberId;
  String doctorName;
  String visitDate;
  String reason;
  String? notes;

  DoctorVisit({this.id, required this.familyMemberId, required this.doctorName, required this.visitDate, required this.reason, this.notes});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'familyMemberId': familyMemberId,
      'doctorName': doctorName,
      'visitDate': visitDate,
      'reason': reason,
      'notes': notes,
    };
  }

  factory DoctorVisit.fromMap(Map<String, dynamic> map) {
    return DoctorVisit(
      id: map['id'],
      familyMemberId: map['familyMemberId'],
      doctorName: map['doctorName'],
      visitDate: map['visitDate'],
      reason: map['reason'],
      notes: map['notes'],
    );
  }
}

class Diagnosis {
  int? id;
  int visitId;
  String diagnosis;
  String date;

  Diagnosis({this.id, required this.visitId, required this.diagnosis, required this.date});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'visitId': visitId,
      'diagnosis': diagnosis,
      'date': date,
    };
  }

  factory Diagnosis.fromMap(Map<String, dynamic> map) {
    return Diagnosis(
      id: map['id'],
      visitId: map['visitId'],
      diagnosis: map['diagnosis'],
      date: map['date'],
    );
  }
}

class MedicalReport {
  int? id;
  int visitId;
  String reportPath;
  String reportName;

  MedicalReport({this.id, required this.visitId, required this.reportPath, required this.reportName});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'visitId': visitId,
      'reportPath': reportPath,
      'reportName': reportName,
    };
  }

  factory MedicalReport.fromMap(Map<String, dynamic> map) {
    return MedicalReport(
      id: map['id'],
      visitId: map['visitId'],
      reportPath: map['reportPath'],
      reportName: map['reportName'],
    );
  }
}

class Medicine {
  int? id;
  int visitId;
  String medicineName;
  String dosage;
  String frequency;
  DateTime startDate;
  DateTime endDate;

  Medicine({this.id, required this.visitId, required this.medicineName, required this.dosage, required this.frequency, required this.startDate, required this.endDate});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'visitId': visitId,
      'medicineName': medicineName,
      'dosage': dosage,
      'frequency': frequency,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
    };
  }

  factory Medicine.fromMap(Map<String, dynamic> map) {
    return Medicine(
      id: map['id'],
      visitId: map['visitId'],
      medicineName: map['medicineName'],
      dosage: map['dosage'],
      frequency: map['frequency'],
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
    );
  }
}

class Reminder {
  int? id;
  int medicineId;
  String reminderTime;
  bool enabled;

  Reminder({this.id, required this.medicineId, required this.reminderTime, this.enabled = true});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'medicineId': medicineId,
      'reminderTime': reminderTime,
      'enabled': enabled ? 1 : 0,
    };
  }

  factory Reminder.fromMap(Map<String, dynamic> map) {
    return Reminder(
      id: map['id'],
      medicineId: map['medicineId'],
      reminderTime: map['reminderTime'],
      enabled: map['enabled'] == 1,
    );
  }
}
