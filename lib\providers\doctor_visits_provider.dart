import 'package:flutter/foundation.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';

class DoctorVisitsProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  List<DoctorVisit> _doctorVisits = [];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  int? _currentFamilyMemberId;

  List<DoctorVisit> get doctorVisits => _filteredDoctorVisits;
  List<DoctorVisit> get allDoctorVisits => _doctorVisits;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;

  List<DoctorVisit> get _filteredDoctorVisits {
    if (_searchQuery.isEmpty) {
      return _doctorVisits;
    }
    return _doctorVisits.where((visit) {
      return visit.doctorName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             visit.reason.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             visit.visitDate.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  Future<void> loadDoctorVisits(int familyMemberId) async {
    _currentFamilyMemberId = familyMemberId;
    _setLoading(true);
    _setError(null);
    
    try {
      _doctorVisits = await _databaseHelper.getDoctorVisits(familyMemberId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load doctor visits: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addDoctorVisit(DoctorVisit visit) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.insertDoctorVisit(visit);
      if (_currentFamilyMemberId != null) {
        await loadDoctorVisits(_currentFamilyMemberId!);
      }
    } catch (e) {
      _setError('Failed to add doctor visit: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  Future<void> updateDoctorVisit(DoctorVisit visit) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.updateDoctorVisit(visit);
      if (_currentFamilyMemberId != null) {
        await loadDoctorVisits(_currentFamilyMemberId!);
      }
    } catch (e) {
      _setError('Failed to update doctor visit: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  Future<void> deleteDoctorVisit(int id) async {
    _setLoading(true);
    _setError(null);
    
    try {
      await _databaseHelper.deleteDoctorVisit(id);
      if (_currentFamilyMemberId != null) {
        await loadDoctorVisits(_currentFamilyMemberId!);
      }
    } catch (e) {
      _setError('Failed to delete doctor visit: ${e.toString()}');
      _setLoading(false);
      rethrow;
    }
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void clearSearch() {
    _searchQuery = '';
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
