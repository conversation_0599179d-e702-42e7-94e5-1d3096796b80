import 'package:flutter/material.dart';
import 'package:medical/services/export_service.dart';
import 'package:medical/services/import_service.dart';
import 'package:medical/utils/responsive.dart';
import 'package:medical/widgets/custom_card.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isExporting = false;
  bool _isImporting = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ResponsiveContainer(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Data Management',
                style: TextStyle(
                  fontSize: Responsive.getTitleFontSize(context),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Export Section
              _buildExportSection(),
              const SizedBox(height: 24),
              
              // Import Section
              _buildImportSection(),
              const SizedBox(height: 24),
              
              // App Information
              _buildAppInfoSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExportSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Export Data',
          style: TextStyle(
            fontSize: Responsive.getSubtitleFontSize(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        InfoCard(
          title: 'Export to PDF',
          subtitle: 'Generate a comprehensive PDF report of all medical records',
          leadingIcon: Icons.picture_as_pdf,
          iconColor: Colors.red,
          trailing: _isExporting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.arrow_forward_ios),
          onTap: _isExporting ? null : _exportToPDF,
        ),
        
        InfoCard(
          title: 'Export to CSV',
          subtitle: 'Export data in spreadsheet format for analysis',
          leadingIcon: Icons.table_chart,
          iconColor: Colors.green,
          trailing: _isExporting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.arrow_forward_ios),
          onTap: _isExporting ? null : _exportToCSV,
        ),
        
        if (!Responsive.isMobile(context))
          InfoCard(
            title: 'Print Records',
            subtitle: 'Print medical records directly',
            leadingIcon: Icons.print,
            iconColor: Colors.blue,
            trailing: _isExporting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.arrow_forward_ios),
            onTap: _isExporting ? null : _printRecords,
          ),
      ],
    );
  }

  Widget _buildImportSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Import Data',
          style: TextStyle(
            fontSize: Responsive.getSubtitleFontSize(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        InfoCard(
          title: 'Import from CSV',
          subtitle: 'Import medical records from a CSV file',
          leadingIcon: Icons.upload_file,
          iconColor: Colors.orange,
          trailing: _isImporting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.arrow_forward_ios),
          onTap: _isImporting ? null : _importFromCSV,
        ),
        
        InfoCard(
          title: 'Download CSV Template',
          subtitle: 'Get a sample CSV file format for importing',
          leadingIcon: Icons.download,
          iconColor: Colors.purple,
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: _downloadTemplate,
        ),
      ],
    );
  }

  Widget _buildAppInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'About',
          style: TextStyle(
            fontSize: Responsive.getSubtitleFontSize(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        InfoCard(
          title: 'MediKeep',
          subtitle: 'Version 1.0.0\nA comprehensive medical records management app',
          leadingIcon: Icons.info,
          iconColor: Colors.blue,
        ),
        
        InfoCard(
          title: 'Privacy & Security',
          subtitle: 'All data is stored locally on your device',
          leadingIcon: Icons.security,
          iconColor: Colors.green,
        ),
      ],
    );
  }

  Future<void> _exportToPDF() async {
    setState(() {
      _isExporting = true;
    });

    try {
      await ExportService().exportToPDF();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PDF exported successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _exportToCSV() async {
    setState(() {
      _isExporting = true;
    });

    try {
      await ExportService().exportToCSV();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('CSV exported successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _printRecords() async {
    setState(() {
      _isExporting = true;
    });

    try {
      await ExportService().printPDF();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Print dialog opened!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Print failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _importFromCSV() async {
    setState(() {
      _isImporting = true;
    });

    try {
      final result = await ImportService().importFromCSV();
      
      if (mounted) {
        if (result.success) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Import Successful'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Family Members: ${result.importedMembers}'),
                  Text('Doctor Visits: ${result.importedVisits}'),
                  Text('Diagnoses: ${result.importedDiagnoses}'),
                  Text('Medicines: ${result.importedMedicines}'),
                  if (result.errors > 0)
                    Text('Errors: ${result.errors}', style: const TextStyle(color: Colors.red)),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Import failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isImporting = false;
        });
      }
    }
  }

  void _downloadTemplate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('CSV template download not implemented yet'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
