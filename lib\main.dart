import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medical/screens/home_screen.dart';
import 'package:medical/services/notification_service.dart';
import 'package:medical/providers/family_provider.dart';
import 'package:medical/providers/doctor_visits_provider.dart';
import 'package:medical/providers/medicines_provider.dart';
import 'package:medical/providers/dashboard_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await NotificationService().initialize();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => FamilyProvider()),
        ChangeNotifierProvider(create: (_) => DoctorVisitsProvider()),
        ChangeNotifierProvider(create: (_) => MedicinesProvider()),
        ChangeNotifierProvider(create: (_) => DashboardProvider()),
      ],
      child: <PERSON><PERSON><PERSON>(
        title: 'MediKeep',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          // Enhanced theme for better mobile/web experience
          appBarTheme: const AppBarTheme(
            elevation: 2,
            centerTitle: true,
          ),
          cardTheme: CardThemeData(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
        home: const HomeScreen(),
      ),
    );
  }
}