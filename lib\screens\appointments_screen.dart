import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_doctor_visit_screen.dart';
import 'package:medical/screens/diagnoses_screen.dart';

class AppointmentsScreen extends StatefulWidget {
  const AppointmentsScreen({super.key});

  @override
  State<AppointmentsScreen> createState() => _AppointmentsScreenState();
}

class _AppointmentsScreenState extends State<AppointmentsScreen> {
  late Future<List<AppointmentInfo>> _appointments;

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  void _loadAppointments() {
    setState(() {
      _appointments = _getAppointments();
    });
  }

  Future<List<AppointmentInfo>> _getAppointments() async {
    final familyMembers = await DatabaseHelper().getFamilyMembers();
    List<AppointmentInfo> appointments = [];
    
    for (final member in familyMembers) {
      final visits = await DatabaseHelper().getDoctorVisits(member.id!);
      for (final visit in visits) {
        appointments.add(AppointmentInfo(
          familyMember: member,
          doctorVisit: visit,
        ));
      }
    }
    
    // Sort appointments by date (most recent first)
    appointments.sort((a, b) {
      final dateA = DateTime.tryParse(a.doctorVisit.visitDate) ?? DateTime.now();
      final dateB = DateTime.tryParse(b.doctorVisit.visitDate) ?? DateTime.now();
      return dateB.compareTo(dateA);
    });
    
    return appointments;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          _loadAppointments();
        },
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  const Icon(Icons.calendar_today, size: 28, color: Colors.blue),
                  const SizedBox(width: 12),
                  const Text(
                    'Appointments',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: _showAddAppointmentDialog,
                  ),
                ],
              ),
            ),
            
            // Appointments List
            Expanded(
              child: FutureBuilder<List<AppointmentInfo>>(
                future: _appointments,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Center(child: Text('Error: ${snapshot.error}'));
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.event_busy, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('No appointments found.'),
                          SizedBox(height: 8),
                          Text(
                            'Add a family member and schedule their doctor visits.',
                            style: TextStyle(color: Colors.grey),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  } else {
                    return _buildAppointmentsList(snapshot.data!);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentsList(List<AppointmentInfo> appointments) {
    final now = DateTime.now();
    final upcomingAppointments = <AppointmentInfo>[];
    final pastAppointments = <AppointmentInfo>[];
    
    for (final appointment in appointments) {
      final visitDate = DateTime.tryParse(appointment.doctorVisit.visitDate);
      if (visitDate != null && visitDate.isAfter(now)) {
        upcomingAppointments.add(appointment);
      } else {
        pastAppointments.add(appointment);
      }
    }
    
    return ListView(
      children: [
        if (upcomingAppointments.isNotEmpty) ...[
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Upcoming Appointments',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ),
          ...upcomingAppointments.map((appointment) => _buildAppointmentCard(appointment, true)),
        ],
        
        if (pastAppointments.isNotEmpty) ...[
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Past Appointments',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          ...pastAppointments.map((appointment) => _buildAppointmentCard(appointment, false)),
        ],
      ],
    );
  }

  Widget _buildAppointmentCard(AppointmentInfo appointment, bool isUpcoming) {
    final visitDate = DateTime.tryParse(appointment.doctorVisit.visitDate);
    final formattedDate = visitDate != null ? _formatDate(visitDate) : appointment.doctorVisit.visitDate;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isUpcoming ? Colors.green : Colors.grey,
          child: Icon(
            isUpcoming ? Icons.schedule : Icons.history,
            color: Colors.white,
          ),
        ),
        title: Text(appointment.familyMember.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Dr. ${appointment.doctorVisit.doctorName}'),
            Text(formattedDate),
            Text(appointment.doctorVisit.reason),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DiagnosesScreen(visit: appointment.doctorVisit),
            ),
          );
        },
      ),
    );
  }

  void _showAddAppointmentDialog() async {
    final familyMembers = await DatabaseHelper().getFamilyMembers();
    
    if (familyMembers.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please add a family member first.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }
    
    if (mounted) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Add Appointment'),
            content: const Text('Select a family member to schedule an appointment for:'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ...familyMembers.map((member) => TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AddDoctorVisitScreen(familyMemberId: member.id!),
                    ),
                  ).then((_) => _loadAppointments());
                },
                child: Text(member.name),
              )),
            ],
          );
        },
      );
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}

class AppointmentInfo {
  final FamilyMember familyMember;
  final DoctorVisit doctorVisit;

  AppointmentInfo({
    required this.familyMember,
    required this.doctorVisit,
  });
}
