import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/services/notification_service.dart';

class AddReminderScreen extends StatefulWidget {
  final Medicine medicine;

  const AddReminderScreen({super.key, required this.medicine});

  @override
  State<AddReminderScreen> createState() => _AddReminderScreenState();
}

class _AddReminderScreenState extends State<AddReminderScreen> {
  final _formKey = GlobalKey<FormState>();
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _enabled = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Reminder'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Medicine Info
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.medicine.medicineName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('Dosage: ${widget.medicine.dosage}'),
                      Text('Frequency: ${widget.medicine.frequency}'),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Time Selection
              const Text(
                'Reminder Time',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              
              Card(
                child: ListTile(
                  leading: const Icon(Icons.access_time),
                  title: Text(
                    '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}',
                    style: const TextStyle(fontSize: 18),
                  ),
                  subtitle: const Text('Tap to change time'),
                  trailing: const Icon(Icons.edit),
                  onTap: _selectTime,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Enable/Disable Switch
              Card(
                child: SwitchListTile(
                  title: const Text('Enable Reminder'),
                  subtitle: const Text('Turn on to receive notifications'),
                  value: _enabled,
                  onChanged: (value) {
                    setState(() {
                      _enabled = value;
                    });
                  },
                ),
              ),
              
              const SizedBox(height: 30),
              
              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveReminder,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Save Reminder',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  Future<void> _saveReminder() async {
    if (_formKey.currentState!.validate()) {
      try {
        // Create reminder time as DateTime
        final now = DateTime.now();
        final reminderDateTime = DateTime(
          now.year,
          now.month,
          now.day,
          _selectedTime.hour,
          _selectedTime.minute,
        );

        final newReminder = Reminder(
          medicineId: widget.medicine.id!,
          reminderTime: reminderDateTime.toIso8601String(),
          enabled: _enabled,
        );

        final reminderId = await DatabaseHelper().insertReminder(newReminder);
        
        if (_enabled) {
          // Schedule notification
          final reminderWithId = Reminder(
            id: reminderId,
            medicineId: widget.medicine.id!,
            reminderTime: reminderDateTime.toIso8601String(),
            enabled: _enabled,
          );
          
          await NotificationService().scheduleMedicineReminder(
            medicine: widget.medicine,
            reminder: reminderWithId,
          );
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reminder saved successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error saving reminder: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
