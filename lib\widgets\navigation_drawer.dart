import 'package:flutter/material.dart';
import 'package:medical/services/navigation_service.dart';
import 'package:medical/utils/responsive.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';

class MedicalNavigationDrawer extends StatefulWidget {
  const MedicalNavigationDrawer({super.key});

  @override
  State<MedicalNavigationDrawer> createState() => _MedicalNavigationDrawerState();
}

class _MedicalNavigationDrawerState extends State<MedicalNavigationDrawer> {
  List<FamilyMember> _recentFamilyMembers = [];
  List<DoctorVisit> _recentVisits = [];
  List<Medicine> _activeMedicines = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecentData();
  }

  Future<void> _loadRecentData() async {
    try {
      final familyMembers = await DatabaseHelper().getFamilyMembers();
      final visits = await DatabaseHelper().getAllDoctorVisits();
      final medicines = await DatabaseHelper().getActiveMedicines();

      if (mounted) {
        setState(() {
          _recentFamilyMembers = familyMembers.take(5).toList();
          _recentVisits = visits.take(5).toList();
          _activeMedicines = medicines.take(5).toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          _buildDrawerHeader(context),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : ListView(
                    padding: EdgeInsets.zero,
                    children: [
                      _buildMainNavigation(context),
                      const Divider(),
                      _buildQuickActions(context),
                      const Divider(),
                      _buildRecentItems(context),
                      const Divider(),
                      _buildSettings(context),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return DrawerHeader(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.medical_services,
            size: 48,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
          const SizedBox(height: 8),
          Text(
            'MediKeep',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
          Text(
            'Medical Records Manager',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainNavigation(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Main Navigation',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        _buildNavTile(
          context,
          'Dashboard',
          Icons.dashboard,
          () {
            Navigator.pop(context);
            NavigationService().goToDashboard();
          },
        ),
        _buildNavTile(
          context,
          'Family Members',
          Icons.people,
          () {
            Navigator.pop(context);
            NavigationService().goToFamily();
          },
        ),
        _buildNavTile(
          context,
          'Appointments',
          Icons.calendar_today,
          () {
            Navigator.pop(context);
            NavigationService().goToAppointments();
          },
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        _buildNavTile(
          context,
          'Add Family Member',
          Icons.person_add,
          () {
            Navigator.pop(context);
            NavigationService().addFamilyMember();
          },
          color: Colors.blue,
        ),
        _buildNavTile(
          context,
          'Schedule Appointment',
          Icons.add_circle,
          () {
            Navigator.pop(context);
            _showAppointmentDialog();
          },
          color: Colors.green,
        ),
        _buildNavTile(
          context,
          'Add Medicine',
          Icons.medication,
          () {
            Navigator.pop(context);
            _showMedicineDialog();
          },
          color: Colors.orange,
        ),
      ],
    );
  }

  Widget _buildRecentItems(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Recent Items',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        if (_recentFamilyMembers.isNotEmpty) ...[
          _buildSectionHeader('Family Members'),
          ..._recentFamilyMembers.map((member) => _buildNavTile(
            context,
            member.name,
            Icons.person,
            () {
              Navigator.pop(context);
              NavigationService().goToDoctorVisits(member);
            },
            subtitle: member.relation,
          )),
        ],
        if (_activeMedicines.isNotEmpty) ...[
          _buildSectionHeader('Active Medicines'),
          ..._activeMedicines.map((medicine) => _buildNavTile(
            context,
            medicine.medicineName,
            Icons.medication,
            () {
              Navigator.pop(context);
              NavigationService().goToReminders(medicine);
            },
            subtitle: medicine.dosage,
          )),
        ],
      ],
    );
  }

  Widget _buildSettings(BuildContext context) {
    return Column(
      children: [
        _buildNavTile(
          context,
          'Settings',
          Icons.settings,
          () {
            Navigator.pop(context);
            NavigationService().goToSettings();
          },
        ),
        _buildNavTile(
          context,
          'Export Data',
          Icons.download,
          () {
            Navigator.pop(context);
            // Handle export
          },
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(32.0, 8.0, 16.0, 4.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
    );
  }

  Widget _buildNavTile(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap, {
    String? subtitle,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: color ?? Theme.of(context).colorScheme.onSurface,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: subtitle != null ? Text(subtitle) : null,
      onTap: onTap,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
    );
  }

  void _showAppointmentDialog() async {
    final familyMembers = await DatabaseHelper().getFamilyMembers();
    
    if (!mounted) return;
    
    if (familyMembers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add a family member first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Schedule Appointment'),
          content: const Text('Select a family member:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ...familyMembers.map((member) => TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                NavigationService().addDoctorVisit(member.id!);
              },
              child: Text(member.name),
            )),
          ],
        );
      },
    );
  }

  void _showMedicineDialog() async {
    final visits = await DatabaseHelper().getAllDoctorVisits();
    
    if (!mounted) return;
    
    if (visits.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add a doctor visit first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Add Medicine'),
          content: const Text('Select a doctor visit:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ...visits.take(5).map((visit) => TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                NavigationService().addMedicine(visit.id!);
              },
              child: Text('${visit.doctorName} - ${visit.visitDate}'),
            )),
          ],
        );
      },
    );
  }
}
