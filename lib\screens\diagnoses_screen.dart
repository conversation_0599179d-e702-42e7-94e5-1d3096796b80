
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_diagnosis_screen.dart';
import 'package:medical/screens/medical_reports_screen.dart';
import 'package:medical/screens/medicines_screen.dart';

class DiagnosesScreen extends StatefulWidget {
  final DoctorVisit visit;

  const DiagnosesScreen({super.key, required this.visit});

  @override
  State<DiagnosesScreen> createState() => _DiagnosesScreenState();
}

class _DiagnosesScreenState extends State<DiagnosesScreen> {
  late Future<List<Diagnosis>> _diagnoses;

  @override
  void initState() {
    super.initState();
    _loadDiagnoses();
  }

  void _loadDiagnoses() {
    setState(() {
      _diagnoses = DatabaseHelper().getDiagnoses(widget.visit.id!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Diagnoses for ${widget.visit.visitDate}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.receipt_long),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MedicalReportsScreen(visit: widget.visit),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.medical_services),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MedicinesScreen(visit: widget.visit),
                ),
              );
            },
          ),
        ],
      ),
      body: FutureBuilder<List<Diagnosis>>(
        future: _diagnoses,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No diagnoses found.'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                final diagnosis = snapshot.data![index];
                return ListTile(
                  title: Text(diagnosis.diagnosis),
                  subtitle: Text(diagnosis.date),
                );
              },
            );
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddDiagnosisScreen(visitId: widget.visit.id!),
            ),
          );
          _loadDiagnoses();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
